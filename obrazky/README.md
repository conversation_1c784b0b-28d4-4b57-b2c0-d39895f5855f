# Anchor Image Uploader

<PERSON>k<PERSON> nahr<PERSON>č obrá<PERSON><PERSON> epizód na Anchor/Spotify for Podcasters platform.

## Funkcie

- 🎯 Automatické načítanie epizód z RSS feedu
- 🖼️ Inteligentné párovanie epizód s obrázkami
- 🤖 Automatické prihlásenie na Anchor
- ⬆️ Hromadné nahrávanie obrázkov
- 📊 Detailné logovanie a reporting
- 🔄 Dry-run režim pre testovanie

## Požiadavky

- Python 3.8+
- Google Chrome browser
- ChromeDriver (automaticky sa stiahne)
- Anchor/Spotify for Podcasters účet

## Inštalácia

1. **Nainštalujte z<PERSON>ti:**
```bash
pip install -r requirements.txt
```

2. **Vytvorte konfiguračný súbor:**
```bash
python anchor_image_uploader.py --create-config
```

3. **Upravte konfiguráciu:**
<PERSON><PERSON><PERSON>te `config_sample.json` a premenujte na `config.json`, potom vyplňte:
```json
{
  "anchor_email": "<EMAIL>",
  "anchor_password": "vase_heslo",
  "rss_url": "https://anchor.fm/s/8db2e1ec/podcast/rss",
  "images_dir": "../images",
  "chrome_driver_path": null,
  "headless": false,
  "wait_timeout": 30
}
```

## Použitie

### Základné spustenie
```bash
python anchor_image_uploader.py
```

### Test bez nahrávania (dry-run)
```bash
python anchor_image_uploader.py --dry-run
```

### Vlastná konfigurácia
```bash
python anchor_image_uploader.py --config moja_config.json
```

## Štruktúra obrázkov

Skript očakáva obrázky v tomto formáte:
```
images/
├── Názov_epizódy_NO_TEXT_retro.png
├── Ďalšia_epizóda_NO_TEXT_retro.png
└── ...
```

## Párovanie epizód s obrázkami

Skript automaticky páruje epizódy s obrázkami na základe:
- Podobnosti názvov
- Spoločných kľúčových slov
- Fuzzy matching algoritmu

## Logovanie

Všetky aktivity sa zaznamenávajú do:
- `anchor_uploader.log` - súbor s logmi
- Konzola - real-time výstup

## Riešenie problémov

### ChromeDriver chyby
```bash
# Automatická inštalácia ChromeDriver
pip install webdriver-manager
```

### Prihlásenie zlyhá
- Skontrolujte prihlasovacie údaje
- Vypnite 2FA dočasne
- Skúste manuálne prihlásenie v prehliadači

### Epizódy sa nenachádzajú
- Skontrolujte RSS URL
- Overte názvy obrázkov
- Použite dry-run pre debugging

## Bezpečnosť

⚠️ **Dôležité upozornenia:**
- Nikdy nezdieľajte konfiguračný súbor s heslami
- Používajte silné heslá
- Pravidelne aktualizujte závislosti

## Podpora

Pre problémy a otázky:
1. Skontrolujte log súbory
2. Spustite v dry-run režime
3. Overte konfiguráciu

## Licencia

Tento skript je vytvorený pre osobné použitie s podcastom Krvavý Dobšinský.
