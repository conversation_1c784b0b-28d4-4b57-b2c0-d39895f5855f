#!/usr/bin/env python3
"""
Inštalačný skript pre Anchor Image Uploader
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Spustí príkaz a zobrazí výsledok"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - úspešné")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - zlyhalo")
        print(f"Chyba: {e.stderr}")
        return False

def check_python_version():
    """Skontroluje verziu Pythonu"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Potrebujete Python 3.8 alebo novší")
        print(f"Aktuálna verzia: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python verzia: {version.major}.{version.minor}.{version.micro}")
    return True

def install_requirements():
    """Nainštaluje požadovan<PERSON> b<PERSON>"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ Súbor requirements.txt neexistuje")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Inštalácia Python balíčkov"
    )

def check_chrome():
    """Skontroluje či je nainštalovaný Chrome"""
    chrome_paths = [
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",  # macOS
        "/usr/bin/google-chrome",  # Linux
        "/usr/bin/chromium-browser",  # Linux Chromium
        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",  # Windows
        "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe"  # Windows 32-bit
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print("✅ Google Chrome nájdený")
            return True
    
    # Skúsi spustiť chrome príkaz
    try:
        subprocess.run(["google-chrome", "--version"], capture_output=True, check=True)
        print("✅ Google Chrome nájdený")
        return True
    except:
        pass
    
    try:
        subprocess.run(["chromium-browser", "--version"], capture_output=True, check=True)
        print("✅ Chromium nájdený")
        return True
    except:
        pass
    
    print("⚠️  Google Chrome nebol nájdený")
    print("Prosím nainštalujte Google Chrome z: https://www.google.com/chrome/")
    return False

def create_sample_config():
    """Vytvorí ukážkovú konfiguráciu"""
    if run_command(
        f"{sys.executable} anchor_image_uploader.py --create-config",
        "Vytvorenie ukážkovej konfigurácie"
    ):
        print("\n📝 Upravte súbor config_sample.json a premenujte ho na config.json")
        print("   Vyplňte vaše prihlasovacie údaje pre Anchor")
        return True
    return False

def run_test():
    """Spustí test"""
    print("\n🧪 Chcete spustiť test párovania? (y/n): ", end="")
    response = input().lower().strip()
    
    if response in ['y', 'yes', 'ano', 'áno']:
        return run_command(
            f"{sys.executable} test_matching.py",
            "Test párovania epizód s obrázkami"
        )
    return True

def main():
    """Hlavná funkcia inštalácie"""
    print("🚀 Anchor Image Uploader - Inštalácia")
    print("=" * 50)
    
    # Kontrola Python verzie
    if not check_python_version():
        sys.exit(1)
    
    # Kontrola Chrome
    check_chrome()
    
    # Inštalácia závislostí
    if not install_requirements():
        print("\n❌ Inštalácia zlyhala")
        sys.exit(1)
    
    # Vytvorenie konfigurácie
    if not create_sample_config():
        print("\n❌ Vytvorenie konfigurácie zlyhalo")
        sys.exit(1)
    
    # Test
    run_test()
    
    print("\n" + "=" * 50)
    print("✅ Inštalácia dokončená!")
    print("\n📋 Ďalšie kroky:")
    print("1. Upravte config_sample.json a premenujte na config.json")
    print("2. Vyplňte vaše Anchor prihlasovacie údaje")
    print("3. Spustite: python anchor_image_uploader.py --dry-run")
    print("4. Ak je všetko v poriadku: python anchor_image_uploader.py")
    print("\n📚 Dokumentácia: README.md")

if __name__ == "__main__":
    main()
