#!/usr/bin/env python3
"""
Test skript pre overenie párovania epizód s obr<PERSON><PERSON>ka<PERSON>
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from anchor_image_uploader import AnchorImageUploader, Config, Episode
import logging

# Nastavenie loggingu
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_matching():
    """Test párovania epizód s obrázkami"""
    
    # Vytvorenie test konfigurácie
    config = Config(
        anchor_email="<EMAIL>",
        anchor_password="test_password",
        rss_url="https://anchor.fm/s/8db2e1ec/podcast/rss",
        images_dir="../images",
        headless=True
    )
    
    # Vytvorenie uploaderu
    uploader = AnchorImageUploader(config)
    
    logger.info("=== TEST NAČÍTANIA EPIZÓD Z RSS ===")
    episodes = uploader.load_episodes_from_rss()
    
    if episodes:
        logger.info(f"Načítaných {len(episodes)} epizód")
        logger.info("Prvých 5 epizód:")
        for i, ep in enumerate(episodes[:5], 1):
            logger.info(f"  {i}. {ep.clean_title}")
    else:
        logger.error("Nepodarilo sa načítať epizódy")
        return
    
    logger.info("\n=== TEST NAČÍTANIA OBRÁZKOV ===")
    uploader.episodes = episodes
    image_files = uploader.load_image_files()
    
    if image_files:
        logger.info(f"Načítaných {len(image_files)} obrázkov")
        logger.info("Prvých 5 obrázkov:")
        for i, (name, path) in enumerate(list(image_files.items())[:5], 1):
            logger.info(f"  {i}. {name}")
    else:
        logger.error("Nepodarilo sa načítať obrázky")
        return
    
    logger.info("\n=== TEST PÁROVANIA ===")
    uploader.image_files = image_files
    matched_episodes = uploader.match_episodes_with_images()
    
    logger.info(f"\nVýsledok: {len(matched_episodes)} spárovaných z {len(episodes)} epizód")
    
    if matched_episodes:
        logger.info("\nSpárované epizódy:")
        for ep in matched_episodes[:10]:  # prvých 10
            image_name = os.path.basename(ep.image_path) if ep.image_path else "N/A"
            logger.info(f"  ✓ {ep.clean_title} -> {image_name}")

def test_similarity():
    """Test similarity algoritmu"""
    uploader = AnchorImageUploader(Config("", "", "", ""))
    
    test_pairs = [
        ("Baba Jaga - kanibal z ruských lesov", "Baba_Jaga_-_kanibal_z_ruských_lesov"),
        ("Babylon 333", "Babylon_333"),
        ("Duchárske príbehy vol 2", "Duchárske_príbehy_vol__2"),
        ("Hrôza za dedinou", "Hrôza_za_dedinou_-_strašidelný_príbeh"),
        ("Completely different title", "Úplne_iný_názov")
    ]
    
    logger.info("\n=== TEST SIMILARITY ALGORITMU ===")
    for title1, title2 in test_pairs:
        similarity = uploader.calculate_similarity(title1, title2)
        logger.info(f"'{title1}' <-> '{title2}': {similarity:.3f}")

if __name__ == "__main__":
    test_matching()
    test_similarity()
