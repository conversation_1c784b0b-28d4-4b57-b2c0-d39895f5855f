#!/usr/bin/env python3
"""
Automatický nahrávač obrázkov na Anchor/Spotify for Podcasters
Autor: Augment Agent
Dátum: 2024

Tento skript automaticky nahráva obrázky epizód na Anchor platform.
"""

import os
import re
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from urllib.parse import urlparse
import xml.etree.ElementTree as ET

import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Konfigurácia logovanie
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('anchor_uploader.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Voliteľné knižnice pre lepší matching
try:
    from fuzzywuzzy import fuzz
    FUZZY_AVAILABLE = True
except ImportError:
    FUZZY_AVAILABLE = False
    logger.warning("fuzzywuzzy nie je nainštalované - použije sa základný matching")

@dataclass
class Episode:
    """Reprezentácia epizódy podcastu"""
    title: str
    url: str
    clean_title: str
    image_path: Optional[str] = None

@dataclass
class Config:
    """Konfigurácia pre uploader"""
    anchor_email: str
    anchor_password: str
    rss_url: str
    images_dir: str
    chrome_driver_path: Optional[str] = None
    headless: bool = False
    wait_timeout: int = 30

class AnchorImageUploader:
    """Hlavná trieda pre nahrávanie obrázkov na Anchor"""
    
    def __init__(self, config: Config):
        self.config = config
        self.driver = None
        self.episodes: List[Episode] = []
        self.image_files: Dict[str, str] = {}
        
    def setup_driver(self) -> webdriver.Chrome:
        """Nastavenie Chrome WebDriver"""
        logger.info("Nastavujem Chrome WebDriver...")
        
        chrome_options = Options()
        if self.config.headless:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")
        
        if self.config.chrome_driver_path:
            driver = webdriver.Chrome(
                executable_path=self.config.chrome_driver_path,
                options=chrome_options
            )
        else:
            # Pokúsi sa nájsť chromedriver automaticky
            driver = webdriver.Chrome(options=chrome_options)
        
        driver.implicitly_wait(10)
        return driver
    
    def normalize_text(self, text: str) -> str:
        """Normalizuje text pre lepšie porovnávanie"""
        # Odstráni HTML tagy a CDATA
        text = re.sub(r'<!\[CDATA\[(.*?)\]\]>', r'\1', text)
        text = re.sub(r'<[^>]+>', '', text)

        # Odstráni emoji a špeciálne znaky na začiatku/konci
        text = re.sub(r'^[🔪⚡️💀👻🎭🌙⭐️🔥💯❤️😱👹🧛‍♂️🧟‍♂️]+\s*', '', text)
        text = re.sub(r'\s*[🔪⚡️💀👻🎭🌙⭐️🔥💯❤️😱👹🧛‍♂️🧟‍♂️]+$', '', text)

        # Odstráni čísla epizód a značky
        text = re.sub(r'^#\s*', '', text)
        text = re.sub(r'\s*\(.*?\)\s*$', '', text)  # odstráni text v zátvorkách na konci

        # Normalizuje diakritiku a špeciálne znaky
        replacements = {
            'á': 'a', 'ä': 'a', 'à': 'a', 'â': 'a',
            'é': 'e', 'ě': 'e', 'è': 'e', 'ê': 'e',
            'í': 'i', 'ì': 'i', 'î': 'i',
            'ó': 'o', 'ô': 'o', 'ò': 'o',
            'ú': 'u', 'ů': 'u', 'ù': 'u', 'û': 'u',
            'ý': 'y', 'ỳ': 'y',
            'č': 'c', 'ć': 'c',
            'ď': 'd',
            'ľ': 'l', 'ĺ': 'l',
            'ň': 'n', 'ń': 'n',
            'ř': 'r', 'ŕ': 'r',
            'š': 's', 'ś': 's',
            'ť': 't',
            'ž': 'z', 'ź': 'z',
            'ô': 'o',
            'ä': 'a',
            'ö': 'o',
            'ü': 'u',
            'ß': 'ss'
        }

        for old, new in replacements.items():
            text = text.replace(old, new)
            text = text.replace(old.upper(), new.upper())

        # Odstráni špeciálne znaky a interpunkciu
        text = re.sub(r'[<>:"/\\|?*.,;!()[\]{}„"‚\'\"]+', ' ', text)
        text = re.sub(r'[-_]+', ' ', text)

        # Nahradí viacnásobné medzery jednou
        text = re.sub(r'\s+', ' ', text)

        # Odstráni medzery na začiatku a konci
        text = text.strip()

        return text.lower()

    def sanitize_title(self, title: str) -> str:
        """Vyčistí názov epizódy pre porovnávanie s názvami súborov"""
        return self.normalize_text(title)
    
    def load_episodes_from_rss(self) -> List[Episode]:
        """Načíta epizódy z RSS feedu"""
        logger.info(f"Načítavam epizódy z RSS: {self.config.rss_url}")
        
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            }
            response = requests.get(self.config.rss_url, headers=headers)
            response.raise_for_status()
            
            root = ET.fromstring(response.content)
            episodes = []
            
            for item in root.findall('.//item'):
                title_elem = item.find('title')
                enclosure_elem = item.find('enclosure')
                
                if title_elem is not None and enclosure_elem is not None:
                    title = title_elem.text
                    audio_url = enclosure_elem.get('url')
                    
                    if title and audio_url:
                        # Vyčistenie názvu od CDATA a HTML tagov
                        clean_title = re.sub(r'<!\[CDATA\[(.*?)\]\]>', r'\1', title)
                        clean_title = self.sanitize_title(clean_title)
                        
                        episode = Episode(
                            title=title,
                            url=audio_url,
                            clean_title=clean_title
                        )
                        episodes.append(episode)
            
            logger.info(f"Načítaných {len(episodes)} epizód")
            return episodes
            
        except Exception as e:
            logger.error(f"Chyba pri načítavaní RSS feedu: {e}")
            return []
    
    def load_image_files(self) -> Dict[str, str]:
        """Načíta zoznam obrázkov z adresára"""
        logger.info(f"Načítavam obrázky z: {self.config.images_dir}")
        
        images_path = Path(self.config.images_dir)
        if not images_path.exists():
            logger.error(f"Adresár s obrázkami neexistuje: {self.config.images_dir}")
            return {}
        
        image_files = {}
        supported_formats = {'.png', '.jpg', '.jpeg', '.gif', '.webp'}
        
        for image_file in images_path.iterdir():
            if image_file.is_file() and image_file.suffix.lower() in supported_formats:
                # Extrahuje názov bez prípony a "_NO_TEXT_retro"
                name = image_file.stem
                name = re.sub(r'_NO_TEXT_retro$', '', name)
                # Nahradí podčiarkovníky medzerami
                name = name.replace('_', ' ')
                name = self.normalize_text(name)

                image_files[name] = str(image_file)
        
        logger.info(f"Načítaných {len(image_files)} obrázkov")
        return image_files
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """Vypočíta podobnosť medzi dvoma textami"""
        # Normalizuje oba texty
        norm_text1 = self.normalize_text(text1)
        norm_text2 = self.normalize_text(text2)

        if FUZZY_AVAILABLE:
            # Použije fuzzy matching ak je dostupný
            return fuzz.ratio(norm_text1, norm_text2) / 100.0
        else:
            # Základný algoritmus na základe spoločných slov
            words1 = set(norm_text1.split())
            words2 = set(norm_text2.split())

            if not words1 or not words2:
                return 0.0

            # Jaccard similarity
            intersection = words1.intersection(words2)
            union = words1.union(words2)

            if not union:
                return 0.0

            jaccard = len(intersection) / len(union)

            # Bonus za presný match celého textu
            if norm_text1 == norm_text2:
                return 1.0

            # Bonus za substring match
            if norm_text1 in norm_text2 or norm_text2 in norm_text1:
                jaccard += 0.2

            return min(jaccard, 1.0)

    def match_episodes_with_images(self) -> List[Episode]:
        """Spáruje epizódy s obrázkami"""
        logger.info("Párujem epizódy s obrázkami...")

        matched_episodes = []
        unmatched_episodes = []
        used_images = set()

        for episode in self.episodes:
            best_match = None
            best_score = 0
            best_image_name = ""

            # Hľadá najlepší match medzi obrázkami
            for image_name, image_path in self.image_files.items():
                if image_path in used_images:
                    continue  # Preskočí už použité obrázky

                # Vypočíta podobnosť
                score = self.calculate_similarity(episode.clean_title, image_name)

                # Bonus za presný match kľúčových slov
                episode_keywords = self.extract_keywords(episode.clean_title)
                image_keywords = self.extract_keywords(image_name)

                keyword_bonus = 0
                for keyword in episode_keywords:
                    if keyword in image_keywords:
                        keyword_bonus += 0.1

                final_score = score + keyword_bonus

                if final_score > best_score and final_score > 0.4:  # minimálna podobnosť 40%
                    best_score = final_score
                    best_match = image_path
                    best_image_name = image_name

            if best_match:
                episode.image_path = best_match
                matched_episodes.append(episode)
                used_images.add(best_match)
                logger.info(f"✓ Spárované ({best_score:.2f}): '{episode.clean_title}' -> '{Path(best_match).name}'")
            else:
                unmatched_episodes.append(episode)
                logger.warning(f"✗ Nespárované: '{episode.clean_title}'")

        logger.info(f"Spárených: {len(matched_episodes)}, Nespárených: {len(unmatched_episodes)}")

        # Zobrazí nespárované epizódy a dostupné obrázky pre debugging
        if unmatched_episodes:
            logger.info("=== NESPÁROVANÉ EPIZÓDY ===")
            for ep in unmatched_episodes[:5]:  # prvých 5
                logger.info(f"  - {ep.clean_title}")

            unused_images = [name for name, path in self.image_files.items() if path not in used_images]
            if unused_images:
                logger.info("=== NEPOUŽITÉ OBRÁZKY ===")
                for img in unused_images[:5]:  # prvých 5
                    logger.info(f"  - {img}")

        return matched_episodes

    def extract_keywords(self, text: str) -> set:
        """Extrahuje kľúčové slová z textu"""
        # Odstráni bežné slová (stop words)
        stop_words = {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'will', 'with', 'the', 'this', 'but', 'they', 'have',
            'had', 'what', 'said', 'each', 'which', 'she', 'do', 'how', 'their',
            'if', 'up', 'out', 'many', 'then', 'them', 'these', 'so', 'some',
            'her', 'would', 'make', 'like', 'into', 'him', 'time', 'two', 'more',
            'go', 'no', 'way', 'could', 'my', 'than', 'first', 'been', 'call',
            'who', 'oil', 'sit', 'now', 'find', 'down', 'day', 'did', 'get',
            'come', 'made', 'may', 'part',
            # Slovenské stop words
            'a', 'aby', 'aj', 'ak', 'ako', 'ale', 'alebo', 'and', 'ani', 'áno',
            'asi', 'až', 'bez', 'bude', 'budem', 'budeš', 'budeme', 'budete',
            'budú', 'by', 'bol', 'bola', 'bolo', 'boli', 'byť', 'čo', 'či',
            'ďalší', 'ďalšia', 'ďalšie', 'dnes', 'do', 'ho', 'ich', 'iné',
            'ja', 'je', 'jeho', 'jej', 'ich', 'im', 'ja', 'je', 'jeden',
            'jedna', 'jedno', 'jeho', 'jej', 'ich', 'im', 'ja', 'je', 'k',
            'kam', 'každý', 'každá', 'každé', 'kde', 'keď', 'kto', 'ktorá',
            'ktoré', 'ktorý', 'ktorou', 'ma', 'mať', 'me', 'medzi', 'mi',
            'mna', 'mne', 'mnou', 'môcť', 'môj', 'moja', 'moje', 'môže',
            'môžem', 'môžeš', 'môžeme', 'môžete', 'môžu', 'mu', 'my', 'na',
            'nad', 'nám', 'náš', 'naša', 'naše', 'nie', 'nech', 'než', 'nič',
            'niektorý', 'niektorá', 'niektoré', 'nové', 'nový', 'nová', 'o',
            'od', 'odo', 'of', 'on', 'ona', 'ono', 'oni', 'ony', 'po', 'pod',
            'podľa', 'pokiaľ', 'potom', 'práve', 'pre', 'preto', 'pretože',
            'prvý', 'prvá', 'prvé', 's', 'sa', 'so', 'si', 'sme', 'sú', 'seba',
            'svoj', 'svoja', 'svoje', 'ta', 'tak', 'také', 'takže', 'tam',
            'te', 'tej', 'to', 'toto', 'toho', 'tom', 'tomto', 'tou', 'tu',
            'túto', 'tvoj', 'tvoja', 'tvoje', 'ty', 'v', 'vo', 'viac', 'však',
            'všetok', 'všetka', 'všetko', 'vy', 'z', 'za', 'zo', 'že'
        }

        words = text.lower().split()
        keywords = {word for word in words if len(word) > 2 and word not in stop_words}
        return keywords
    
    def login_to_anchor(self) -> bool:
        """Prihlási sa na Anchor"""
        logger.info("Prihlasujem sa na Anchor...")
        
        try:
            self.driver.get("https://anchor.fm/login")
            wait = WebDriverWait(self.driver, self.config.wait_timeout)
            
            # Čaká na načítanie prihlasovacieho formulára
            email_field = wait.until(EC.presence_of_element_located((By.NAME, "email")))
            password_field = self.driver.find_element(By.NAME, "password")
            
            # Vyplní prihlasovacie údaje
            email_field.clear()
            email_field.send_keys(self.config.anchor_email)
            
            password_field.clear()
            password_field.send_keys(self.config.anchor_password)
            
            # Klikne na tlačidlo prihlásenia
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            
            # Čaká na presmerovanie po úspešnom prihlásení
            wait.until(EC.url_contains("dashboard"))
            logger.info("✓ Úspešne prihlásený")
            return True
            
        except Exception as e:
            logger.error(f"Chyba pri prihlasovaní: {e}")
            return False

    def navigate_to_episodes(self) -> bool:
        """Naviguje na stránku s epizódami"""
        logger.info("Navigujem na stránku s epizódami...")

        try:
            # Hľadá link na epizódy v navigácii
            wait = WebDriverWait(self.driver, self.config.wait_timeout)

            # Možné selektory pre navigáciu na epizódy
            possible_selectors = [
                "//a[contains(text(), 'Episodes')]",
                "//a[contains(text(), 'Epizódy')]",
                "//a[contains(@href, 'episodes')]",
                "//nav//a[contains(text(), 'Episodes')]"
            ]

            episodes_link = None
            for selector in possible_selectors:
                try:
                    episodes_link = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    break
                except TimeoutException:
                    continue

            if episodes_link:
                episodes_link.click()
                time.sleep(3)  # Čaká na načítanie stránky
                logger.info("✓ Navigácia na epizódy úspešná")
                return True
            else:
                logger.error("Nenašiel sa link na epizódy")
                return False

        except Exception as e:
            logger.error(f"Chyba pri navigácii na epizódy: {e}")
            return False

    def find_episode_on_page(self, episode: Episode) -> Optional[object]:
        """Nájde epizódu na stránke"""
        try:
            # Hľadá epizódu podľa názvu
            episode_elements = self.driver.find_elements(By.XPATH, f"//div[contains(text(), '{episode.clean_title[:30]}')]")

            if not episode_elements:
                # Skúsi hľadať podľa čiastočného názvu
                words = episode.clean_title.split()[:3]  # prvé 3 slová
                for word in words:
                    if len(word) > 3:  # ignoruje krátke slová
                        episode_elements = self.driver.find_elements(By.XPATH, f"//div[contains(text(), '{word}')]")
                        if episode_elements:
                            break

            return episode_elements[0] if episode_elements else None

        except Exception as e:
            logger.error(f"Chyba pri hľadaní epizódy '{episode.clean_title}': {e}")
            return None

    def upload_image_for_episode(self, episode: Episode) -> bool:
        """Nahráva obrázok pre konkrétnu epizódu"""
        logger.info(f"Nahrávam obrázok pre epizódu: {episode.clean_title}")

        try:
            # Nájde epizódu na stránke
            episode_element = self.find_episode_on_page(episode)
            if not episode_element:
                logger.warning(f"Epizóda nenájdená na stránke: {episode.clean_title}")
                return False

            # Klikne na epizódu pre editáciu
            episode_element.click()
            time.sleep(2)

            wait = WebDriverWait(self.driver, self.config.wait_timeout)

            # Hľadá tlačidlo pre editáciu alebo nahrávanie obrázka
            edit_selectors = [
                "//button[contains(text(), 'Edit')]",
                "//button[contains(text(), 'Upraviť')]",
                "//a[contains(text(), 'Edit')]",
                "//button[contains(@class, 'edit')]"
            ]

            edit_button = None
            for selector in edit_selectors:
                try:
                    edit_button = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    break
                except TimeoutException:
                    continue

            if edit_button:
                edit_button.click()
                time.sleep(2)

            # Hľadá input pre nahrávanie obrázka
            image_input_selectors = [
                "//input[@type='file']",
                "//input[contains(@accept, 'image')]",
                "//input[contains(@name, 'image')]",
                "//input[contains(@name, 'artwork')]"
            ]

            image_input = None
            for selector in image_input_selectors:
                try:
                    image_input = self.driver.find_element(By.XPATH, selector)
                    break
                except NoSuchElementException:
                    continue

            if image_input:
                # Nahráva obrázok
                image_input.send_keys(episode.image_path)
                time.sleep(3)

                # Hľadá a klikne tlačidlo pre uloženie
                save_selectors = [
                    "//button[contains(text(), 'Save')]",
                    "//button[contains(text(), 'Uložiť')]",
                    "//button[contains(text(), 'Update')]",
                    "//button[contains(@type, 'submit')]"
                ]

                save_button = None
                for selector in save_selectors:
                    try:
                        save_button = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                        break
                    except TimeoutException:
                        continue

                if save_button:
                    save_button.click()
                    time.sleep(3)
                    logger.info(f"✓ Obrázok úspešne nahraný pre: {episode.clean_title}")
                    return True
                else:
                    logger.warning(f"Nenašlo sa tlačidlo pre uloženie pre: {episode.clean_title}")
                    return False
            else:
                logger.warning(f"Nenašiel sa input pre nahrávanie obrázka pre: {episode.clean_title}")
                return False

        except Exception as e:
            logger.error(f"Chyba pri nahrávaní obrázka pre '{episode.clean_title}': {e}")
            return False

    def process_all_episodes(self) -> Tuple[int, int]:
        """Spracuje všetky epizódy"""
        logger.info("Začínam spracovávanie všetkých epizód...")

        successful_uploads = 0
        failed_uploads = 0

        matched_episodes = self.match_episodes_with_images()

        for i, episode in enumerate(matched_episodes, 1):
            logger.info(f"[{i}/{len(matched_episodes)}] Spracovávam: {episode.clean_title}")

            if self.upload_image_for_episode(episode):
                successful_uploads += 1
            else:
                failed_uploads += 1

            # Krátka pauza medzi epizódami
            time.sleep(2)

        logger.info(f"Dokončené! Úspešné: {successful_uploads}, Neúspešné: {failed_uploads}")
        return successful_uploads, failed_uploads

    def run(self) -> bool:
        """Hlavná metóda pre spustenie uploaderu"""
        logger.info("=== Spúšťam Anchor Image Uploader ===")

        try:
            # Nastavenie WebDriver
            self.driver = self.setup_driver()

            # Načítanie epizód a obrázkov
            self.episodes = self.load_episodes_from_rss()
            if not self.episodes:
                logger.error("Nenačítali sa žiadne epizódy")
                return False

            self.image_files = self.load_image_files()
            if not self.image_files:
                logger.error("Nenačítali sa žiadne obrázky")
                return False

            # Prihlásenie na Anchor
            if not self.login_to_anchor():
                logger.error("Nepodarilo sa prihlásiť na Anchor")
                return False

            # Navigácia na epizódy
            if not self.navigate_to_episodes():
                logger.error("Nepodarilo sa navigovať na epizódy")
                return False

            # Spracovanie všetkých epizód
            successful, failed = self.process_all_episodes()

            logger.info(f"=== Dokončené! Úspešné: {successful}, Neúspešné: {failed} ===")
            return successful > 0

        except Exception as e:
            logger.error(f"Kritická chyba: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver ukončený")


def load_config_from_file(config_path: str = "config.json") -> Optional[Config]:
    """Načíta konfiguráciu zo súboru"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        return Config(
            anchor_email=config_data['anchor_email'],
            anchor_password=config_data['anchor_password'],
            rss_url=config_data.get('rss_url', 'https://anchor.fm/s/8db2e1ec/podcast/rss'),
            images_dir=config_data.get('images_dir', '../images'),
            chrome_driver_path=config_data.get('chrome_driver_path'),
            headless=config_data.get('headless', False),
            wait_timeout=config_data.get('wait_timeout', 30)
        )
    except Exception as e:
        logger.error(f"Chyba pri načítavaní konfigurácie: {e}")
        return None


def create_sample_config():
    """Vytvorí ukážkovú konfiguráciu"""
    sample_config = {
        "anchor_email": "<EMAIL>",
        "anchor_password": "vase_heslo",
        "rss_url": "https://anchor.fm/s/8db2e1ec/podcast/rss",
        "images_dir": "../images",
        "chrome_driver_path": None,
        "headless": False,
        "wait_timeout": 30
    }

    with open("config_sample.json", 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, indent=2, ensure_ascii=False)

    logger.info("Vytvorená ukážková konfigurácia: config_sample.json")


def main():
    """Hlavná funkcia"""
    import argparse

    parser = argparse.ArgumentParser(description='Anchor Image Uploader')
    parser.add_argument('--config', default='config.json', help='Cesta ku konfiguračnému súboru')
    parser.add_argument('--create-config', action='store_true', help='Vytvorí ukážkovú konfiguráciu')
    parser.add_argument('--dry-run', action='store_true', help='Spustí bez nahrávania (len test)')

    args = parser.parse_args()

    if args.create_config:
        create_sample_config()
        return

    # Načítanie konfigurácie
    config = load_config_from_file(args.config)
    if not config:
        logger.error(f"Nepodarilo sa načítať konfiguráciu zo súboru: {args.config}")
        logger.info("Spustite s --create-config pre vytvorenie ukážkovej konfigurácie")
        return

    # Spustenie uploaderu
    uploader = AnchorImageUploader(config)

    if args.dry_run:
        logger.info("=== DRY RUN MODE ===")
        # Len načíta a spáruje epizódy s obrázkami
        uploader.episodes = uploader.load_episodes_from_rss()
        uploader.image_files = uploader.load_image_files()
        uploader.match_episodes_with_images()
    else:
        success = uploader.run()
        if success:
            logger.info("Nahrávanie dokončené úspešne!")
        else:
            logger.error("Nahrávanie zlyhalo!")


if __name__ == "__main__":
    main()
